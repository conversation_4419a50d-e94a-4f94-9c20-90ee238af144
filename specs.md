# Next Agent - Development Specifications

## Project Overview

**Next** is a personal productivity agent designed for a single user to assist with daily business tasks including document management, question answering, planning, and content generation.

### Core Requirements
- **Single-user application** for personal productivity
- **Desktop application** built with Electron
- **Cloud-hosted backend** on Google Cloud Platform (GCP)
- **OneDrive integration** for file access (read, write, update)
- **TogetherAI integration** for LLM capabilities with model selection

## Technical Architecture

### Technology Stack
- **Frontend**: Electron desktop application (Node.js + HTML/CSS/JS)
- **Backend API**: FastAPI hosted on GCP Cloud Run
- **LLM Provider**: TogetherAI API with multiple model support
- **Cloud Platform**: Google Cloud Platform
- **File Integration**: Microsoft OneDrive via Graph API
- **Vector Database**: Chroma or Pinecone for document embeddings
- **Local Storage**: SQLite for conversation history and preferences
- **Caching**: Local file cache + GCP Cloud Storage

### Supported Models (TogetherAI)
- **Llama 3.1 70B**: Complex reasoning and analysis tasks
- **Llama 3.1 8B**: Quick responses and simple queries
- **CodeLlama**: Code generation and technical documentation
- **Mixtral 8x7B**: General purpose with balanced performance/cost

## System Components

### 1. Electron Desktop Application

#### Main Process Responsibilities
- TogetherAI API communication
- OneDrive Graph API integration
- Local database management (SQLite)
- Secure credential storage
- File system operations and caching

#### Renderer Process Features
- Chat interface with conversation history
- Model selection dropdown/interface
- OneDrive file browser integration
- Document viewer with AI annotations
- Settings panel for preferences and sync options
- Offline mode indicator and functionality

#### Required UI Components
```
├── Chat Interface
│   ├── Message history display
│   ├── Input field with send button
│   ├── Model selection dropdown
│   └── Typing indicators and status
├── File Management Panel
│   ├── OneDrive folder tree view
│   ├── File upload/download controls
│   ├── Sync status indicators
│   └── File preview capabilities
├── Settings Panel
│   ├── Authentication management
│   ├── Model preferences
│   ├── Sync configuration
│   └── Cache management
└── Status Bar
    ├── Connection status
    ├── Sync status
    └── Current model indicator
```

### 2. GCP Backend Infrastructure

#### Cloud Run API Service
- FastAPI application handling:
  - TogetherAI model orchestration
  - Document processing and embedding generation
  - Vector database operations
  - OneDrive webhook handling (optional)

#### Cloud Storage
- Cached document storage
- Embedding backups
- Application logs and metrics

#### Cloud Scheduler
- Periodic OneDrive synchronization
- Cache cleanup and maintenance
- Health checks and monitoring

### 3. OneDrive Integration

#### Authentication Flow
```python
# OAuth 2.0 flow for OneDrive access
SCOPES = [
    'https://graph.microsoft.com/Files.ReadWrite.All',
    'https://graph.microsoft.com/Sites.ReadWrite.All'
]
```

#### File Operations
- **Read**: Fetch file contents and metadata
- **Write**: Create new files in specified folders
- **Update**: Modify existing files with version tracking
- **Monitor**: Delta sync for change detection

#### Supported File Types
- Microsoft Office files (.docx, .xlsx, .pptx)
- PDF documents
- Plain text files (.txt, .md)
- Code files (various extensions)

### 4. Document Processing Pipeline

#### Ingestion Workflow
```
OneDrive File → Download → Parse Content → Chunk Text → Generate Embeddings → Store in Vector DB
```

#### File Processing Strategies
- **Text Extraction**: Use appropriate libraries for each file type
- **Chunking**: Semantic chunking with overlap for context preservation
- **Embedding Generation**: Use TogetherAI embedding models
- **Metadata Storage**: File path, modification date, content type, chunk relationships

### 5. Agent Capabilities

#### Core Functions
- **Document Q&A**: RAG-based question answering using personal knowledge base
- **File Management**: Create, update, organize OneDrive files
- **Content Generation**: Reports, summaries, documentation with context from existing files
- **Personal Planning**: Task management and activity planning

#### Model Selection Logic
```javascript
const selectModel = (taskType, complexity, responseTimeReq) => {
  if (taskType === 'code' || taskType === 'technical') return 'CodeLlama';
  if (complexity === 'high' && responseTimeReq === 'flexible') return 'Llama-3.1-70B';
  if (responseTimeReq === 'fast') return 'Llama-3.1-8B';
  return 'Mixtral-8x7B'; // default balanced option
};
```

## Implementation Phases

### Phase 1: MVP Foundation (Weeks 1-4)
1. **Electron App Setup**
   - Basic window and menu structure
   - Simple chat interface
   - Settings panel framework

2. **Authentication Implementation**
   - OneDrive OAuth 2.0 flow
   - TogetherAI API key management
   - GCP service account setup

3. **Basic OneDrive Integration**
   - File listing and browsing
   - Basic file download/upload
   - Simple file preview

4. **TogetherAI Integration**
   - API client implementation
   - Single model chat functionality
   - Error handling and retry logic

### Phase 2: Core Features (Weeks 5-8)
1. **Document Processing**
   - File content extraction
   - Text chunking and embedding
   - Vector database integration

2. **Multi-Model Support**
   - Model selection interface
   - Dynamic model switching
   - Performance monitoring

3. **Enhanced File Management**
   - File creation and editing
   - Sync status indicators
   - Conflict resolution

4. **RAG Implementation**
   - Context retrieval system
   - Document-aware responses
   - Citation and source tracking

### Phase 3: Advanced Features (Weeks 9-12)
1. **Offline Capabilities**
   - Local document caching
   - Offline conversation history
   - Sync queue for offline actions

2. **Performance Optimization**
   - Intelligent caching strategies
   - API call optimization
   - Response time improvements

3. **Advanced Agent Features**
   - Task planning and scheduling
   - Multi-document analysis
   - Automated report generation

4. **Polish and UX**
   - Error handling improvements
   - User experience enhancements
   - Documentation and help system

## Security and Privacy

### Data Protection
- **Local Encryption**: Sensitive data encrypted at rest
- **Secure Communication**: HTTPS/TLS for all API calls
- **Credential Management**: Secure storage using Electron's safeStorage API
- **Access Control**: Granular OneDrive folder permissions

### Privacy Considerations
- All data processing in personal GCP environment
- No third-party data sharing
- Local conversation history with optional cloud backup
- User control over data retention and deletion

## Configuration and Environment

### Environment Variables
```bash
# TogetherAI Configuration
TOGETHER_API_KEY=your_api_key_here
TOGETHER_BASE_URL=https://api.together.xyz/v1

# Microsoft Graph API
MICROSOFT_CLIENT_ID=your_client_id
MICROSOFT_CLIENT_SECRET=your_client_secret
MICROSOFT_REDIRECT_URI=http://localhost:3000/auth/callback

# GCP Configuration
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
GCP_PROJECT_ID=your_project_id
GCP_REGION=us-central1

# Vector Database
CHROMA_HOST=localhost
CHROMA_PORT=8000
```

### Required Dependencies

#### Electron App (package.json)
```json
{
  "dependencies": {
    "electron": "^latest",
    "@microsoft/microsoft-graph-client": "^3.0.0",
    "sqlite3": "^5.0.0",
    "axios": "^1.0.0",
    "mammoth": "^1.6.0",
    "pdf-parse": "^1.1.1",
    "node-xlsx": "^0.23.0"
  }
}
```

#### Backend API (requirements.txt)
```txt
fastapi==0.104.1
uvicorn==0.24.0
chromadb==0.4.15
sentence-transformers==2.2.2
python-multipart==0.0.6
google-cloud-storage==2.10.0
python-docx==1.1.0
PyPDF2==3.0.1
openpyxl==3.1.2
```

## Success Metrics and Monitoring

### Key Performance Indicators
- Response time for different query types
- OneDrive sync accuracy and speed
- Model selection effectiveness
- User satisfaction with generated content
- System uptime and reliability

### Monitoring Setup
- Application performance metrics
- API usage and cost tracking
- Error logging and alerting
- User interaction analytics

## Development Guidelines

### Code Organization
```
next-agent/
├── electron-app/
│   ├── src/
│   │   ├── main/           # Main process
│   │   ├── renderer/       # UI components
│   │   └── shared/         # Shared utilities
├── backend-api/
│   ├── app/
│   │   ├── models/         # Data models
│   │   ├── services/       # Business logic
│   │   └── api/            # API endpoints
└── infrastructure/
    ├── terraform/          # GCP infrastructure
    └── docker/             # Container configurations
```

### Testing Strategy
- Unit tests for core business logic
- Integration tests for API endpoints
- E2E tests for critical user workflows
- Performance testing for document processing
- Security testing for authentication flows

This specification document should provide comprehensive guidance for implementing the Next agent while maintaining flexibility for iterative development and feature expansion.